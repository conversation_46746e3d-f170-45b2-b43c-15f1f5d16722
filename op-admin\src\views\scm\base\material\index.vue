<template>
    <el-row :gutter="20">
    <!-- 左侧部门树 -->
    <el-col :span="4" :xs="24">
      <ContentWrap class="h-1/1">
        <category-tree
            :form="materialCategory"
            :defaultProps="defaultProps"
            :nodeKey="nodeKey"
            @select-category="handleSelectCategory"
            :defaultExpandedKeys="defaultExpandedKeys"
          />
      </ContentWrap>
    </el-col>
    <el-col :span="20" :xs="24">
    <ContentWrap>
      <!-- 搜索工作栏 -->
      <el-form
        class="-mb-15px"
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        label-width="68px"
      >
        <!-- <el-form-item label="物料编码" prop="code">
          <el-input
            v-model="queryParams.code"
            placeholder="请输入物料编码"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
      
        <el-form-item label="编码全称" prop="fullCode">
          <el-input
            v-model="queryParams.fullCode"
            placeholder="请输入物料编码全称"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item> -->
        <el-form-item label="物料全称" prop="fullName">
          <el-input
            v-model="queryParams.fullName"
            placeholder="请输入物料全称"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="分类" prop="type">
          <el-select
            v-model="queryParams.type"
            placeholder="请选择分类"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.MATERIAL_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="形态" prop="form">
          <el-select
            v-model="queryParams.form"
            placeholder="请选择形态"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.MATERIAL_STATE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="子分类" prop="subType">
          <el-select
            v-model="queryParams.subType"
            placeholder="请选择子分类"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.PRODUCT_SUB_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="物料名称" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入物料名称"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item> -->
        
        <el-form-item label="物料规格" prop="spec">
          <el-input
            v-model="queryParams.spec"
            placeholder="请输入物料规格"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="客供" prop="isClientProvided">
          <el-select
            v-model="queryParams.isClientProvided"
            placeholder="请选择客供"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getBoolDictOptions(DICT_TYPE.COMMON_YES_NO)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker
            v-model="queryParams.createTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
          <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
          <el-button
            type="primary"
            plain
            @click="openForm('create')"
            v-hasPermi="['base:material:create']"
          >
            <Icon icon="ep:plus" class="mr-5px" /> 新增
          </el-button>
          <el-button
            type="success"
            plain
            @click="handleExport"
            :loading="exportLoading"
            v-hasPermi="['base:material:export']"
          >
            <Icon icon="ep:download" class="mr-5px" /> 导出
          </el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>

    <!-- 列表 -->
    <ContentWrap>
      <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
        <el-table-column label="编号" align="center" prop="id" />
        <el-table-column label="物料编码" align="left" prop="code" />
        <!-- <el-table-column label="分类编号" align="left" prop="categoryCode" /> -->
        <!-- <el-table-column label="物料编码全称" align="center" prop="fullCode" /> -->
        <el-table-column label="分类" align="left" prop="type" width="100px">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.MATERIAL_TYPE" :value="scope.row.type" />
          </template>
        </el-table-column>
        <el-table-column label="形态" align="left" prop="form">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.MATERIAL_STATE" :value="scope.row.form" />
          </template>
        </el-table-column>
        <el-table-column label="子分类" align="left" prop="subType">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.PRODUCT_SUB_TYPE" :value="scope.row.subType" />
          </template>
        </el-table-column>
        <el-table-column label="物料名称" align="left" prop="name" width="180px"/>
        <!-- <el-table-column label="物料全称" align="center" prop="fullName" /> -->
        <el-table-column label="物料规格" align="left" prop="spec" />
        <!-- <el-table-column label="物料规格数量" align="center" prop="specQuantity" /> -->
        <!-- <el-table-column label="物料规格单位" align="center" prop="specUnit" /> -->
        <el-table-column label="库存量" align="center" prop="inventory" />
        <el-table-column label="主单位" align="center" prop="unit">
          <template #default="scope">
                <!-- 单位对象在unitmap中-->
                <el-tag type="primary">{{ (scope.row.unit && unitMap[(scope.row.unit)])?(unitMap[(scope.row.unit)]).name : scope.row.unit }}</el-tag>  
              </template>
        </el-table-column>
        <el-table-column label="辅助单位" align="center" prop="auxiliaryUnit">
          <template #default="scope">
                <!-- 单位对象在unitmap中 -->
                <el-tag v-if="scope.row.auxiliaryUnit" type="primary">{{ (scope.row.auxiliaryUnit && unitMap[(scope.row.auxiliaryUnit)])?(unitMap[(scope.row.auxiliaryUnit)]).name : scope.row.auxiliaryUnit }}</el-tag>  
              </template>
        </el-table-column>
        <el-table-column label="最新采购价格" align="right" prop="purchasePrice" width="120px"/>
        <!-- <el-table-column label="销售价格" align="right" prop="salePrice" /> -->
        <el-table-column label="采购价格" align="right" prop="averagePurchasePrice" width="100px"/>
        <el-table-column label="价格单位" align="center" prop="priceUnit" />
        <el-table-column label="元素组成" align="center" prop="elements" width="150px">
          <template #default="scope">
            <template v-for="(element, index) in scope.row.elements" :key="index">
              <el-tag type="primary" size="small" class="inlineTag" style="margin-right: -1px;">{{ element.element }}:{{ element.quantity }}{{ element.unit }}</el-tag>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="图片" align="center" prop="url" />
        <el-table-column label="客供" align="center" prop="isClientProvided">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.COMMON_YES_NO" :value="scope.row.isClientProvided" />
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
        <!-- <el-table-column label="其他信息" align="center" prop="otherInfo" /> -->
        <el-table-column label="存货科目" align="center" prop="inventoryAccountId">
          <template #default="scope">
            <!-- 科目对象在subjectMap中-->
             <el-tag v-if="scope.row.inventoryAccountId" type="primary">{{ (scope.row.inventoryAccountId && subjectMap[(scope.row.inventoryAccountId)])?(subjectMap[(scope.row.inventoryAccountId)]).subjectName : scope.row.inventoryAccountId }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="销售收入科目" align="center" prop="saleAccountId" width="120px">
          <template #default="scope">
            <!-- 科目对象在subjectMap中-->
             <el-tag v-if="scope.row.saleAccountId" type="primary">{{ (scope.row.saleAccountId && subjectMap[(scope.row.saleAccountId)])?(subjectMap[(scope.row.saleAccountId)]).subjectName : scope.row.saleAccountId }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="销售成本科目" align="center" prop="saleCostAccountId"  width="120px">
          <template #default="scope">
            <!-- 科目对象在subjectMap中-->
             <el-tag v-if="scope.row.saleCostAccountId" type="primary">{{ (scope.row.saleCostAccountId && subjectMap[(scope.row.saleCostAccountId)])?(subjectMap[(scope.row.saleCostAccountId)]).subjectName : scope.row.saleCostAccountId }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="成本差异科目" align="center" prop="costDiffAccountId"  width="120px">
          <template #default="scope">
            <!-- 科目对象在subjectMap中-->
             <el-tag v-if="scope.row.costDiffAccountId" type="primary">{{ (scope.row.costDiffAccountId && subjectMap[(scope.row.costDiffAccountId)])?(subjectMap[(scope.row.costDiffAccountId)]).subjectName : scope.row.costDiffAccountId }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="代管科目" align="center" prop="adminAccountId"  width="120px">
          <template #default="scope">
            <!-- 科目对象在subjectMap中-->
             <el-tag v-if="scope.row.adminAccountId" type="primary">{{ (scope.row.adminAccountId && subjectMap[(scope.row.adminAccountId)])?(subjectMap[(scope.row.adminAccountId)]).subjectName : scope.row.adminAccountId }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="税目科目" align="center" prop="rateAccountId"  width="120px">
          <template #default="scope">
            <!-- 科目对象在subjectMap中-->
             <el-tag v-if="scope.row.rateAccountId" type="primary">{{ (scope.row.rateAccountId && subjectMap[(scope.row.rateAccountId)])?(subjectMap[(scope.row.rateAccountId)]).subjectName : scope.row.rateAccountId }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="成本项目" align="center" prop="costProjectId"  width="120px">
          <template #default="scope">
            <!-- 科目对象在subjectMap中-->
             <el-tag v-if="scope.row.costProjectId" type="primary">{{ (scope.row.costProjectId && subjectMap[(scope.row.costProjectId)])?(subjectMap[(scope.row.costProjectId)]).subjectName : scope.row.costProjectId }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="二维码" align="center" prop="qrCode"  width="120px">
          <template #default="scope">
            <!-- 科目对象在subjectMap中-->
             <el-tag v-if="scope.row.qrCode" type="primary">{{ (scope.row.qrCode && subjectMap[(scope.row.qrCode)])?(subjectMap[(scope.row.qrCode)]).subjectName : scope.row.qrCode }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="创建时间"
          align="center"
          prop="createTime"
          :formatter="dateFormatter"
          width="180px"
        />
        <!-- <el-table-column label="所属公司名称" align="center" prop="companyName" width="180px"/> -->
        <el-table-column label="操作" align="center" min-width="200px" fixed="right">
          <template #default="scope">
            <el-button
              link
              type="primary"
              @click="openForm('update', scope.row.id)"
              v-hasPermi="['base:material:update']"
            >
            <Icon icon="ep:edit" class="mr-5px" />
            </el-button>
            <el-button
              link
              type="danger"
              @click="handleDelete(scope.row.id)"
              v-hasPermi="['base:material:delete']"
            >
              <Icon icon="ep-delete" class="mr-5px" />
            </el-button>
            <el-button
              link
              type="success"
              @click="handlePrice(scope.row)"
              v-hasPermi="['base:material:delete']"
            >
              设置价格
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </ContentWrap>
  </el-col>
</el-row>
  <!-- 表单弹窗：添加/修改 -->
  <MaterialForm ref="formRef" @success="getList" />
  <PriceDialog ref="priceRef" />
</template>

<script setup lang="ts">
import { getStrDictOptions, getBoolDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { MaterialApi, MaterialVO } from '@/api/scm/base/material/index'
import MaterialForm from './MaterialForm.vue'
import { CategoryApi } from "@/api/scm/base/category/index";
import CategoryTree from "@/components/CategoryTree/index.vue";
import PriceDialog from '../materialprice/PriceDialog.vue'
import { UnitApi } from "@/api/scm/base/unit/index";
import { AccountSubjectApi } from "@/api/scm/finance/accountsubject/index";

/** 物料信息 列表 */
defineOptions({ name: 'Material' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<MaterialVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  code: undefined,
  categoryCode: undefined,
  fullCode: undefined,
  type: undefined,
  form: undefined,
  subType: undefined,
  name: undefined,
  fullName: undefined,
  spec: undefined,
  isClientProvided: undefined,
  createTime: [],
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

const nodeKey = ref("categoryCode");
const categoryFullName = ref("");
const form = ref("")
const materialType = ref("")
const subType = ref("")

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await MaterialApi.getMaterialPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  if (type === 'add') {
    formRef.value.title = '添加物料信息';
  } else {
    formRef.value.title = '修改物料信息';
  }
  formRef.value.open(type, id, categoryFullName, materialType, form, subType, materialCategory.value, unitList.value)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await MaterialApi.deleteMaterial(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await MaterialApi.exportMaterial(queryParams)
    download.excel(data, '物料信息.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

interface Category {
  categoryName: string;
  categoryId: number;
  fullName: string;
  children?: Category[];
}

const materialCategory = ref<Category[]>([]);
const categoryMap = ref<Record<string, any>>({});
const categoryType = ref([]);
const defaultExpandedKeys = ref([]);
const defaultExpandedCategoryKeys = ref([0]);
const priceMaterial = ref({
  id: null,
  code: null,
  name: null,
});
const openPrice = ref(false);
const treeLoading = ref(true);
// uniap是一个map对象，用于存储单位信息
const unitMap = ref({});
const allExpandedKeys = ref([]);
const unitList = ref([]);
const getCategoryList = async () => {
  const response = await CategoryApi.getCategoryList({
    pageNum: 1,
    pageSize: 200,
    categoryType: "material",
  });
  categoryType.value = response;
  materialCategory.value = [
    {
      categoryName: "产品分类",
      categoryCode: '0',
      categoryId: 0,
      fullName: "产品分类",
      children: buildTree(response),
    },
  ];
  // 将categoryType中的数据转换为map对象，categoryType中的元素可能含有children对象，写一个递归函数处理

  categoryType.value.forEach((item) => {
    categoryMap.value[item.fullCode] = item;
    if (item.children && item.children.length > 0) {
      // 使用递归函数处理子节点
      buildCategoryMap(item.children, categoryMap.value);
    }
  });

  defaultExpandedKeys.value = ['0'];
  treeLoading.value = false;
};


function buildTree(data, parentId = 0) {
  const tree = [];
  for (const item of data) {
    if (item.parentCategoryId === parentId) {
      const children = buildTree(data, item.categoryId);
      if (children.length > 0) {
        item.children = children;
      }
      tree.push(item);
    }
  }
  return tree;
}

// 递归处理分类数据，构建categoryMap
function buildCategoryMap(data, map = {}) {
  data.forEach(item => {
    // 将当前项添加到map中
    map[item.fullCode] = item;
    
    // 如果有子节点，递归处理子节点
    if (item.children && item.children.length > 0) {
      buildCategoryMap(item.children, map);
    }
  });
  return map;
}

const defaultProps = {
  children: "children",
  label: "categoryName",
  key: "categoryCode",
};

const handleSelectCategory = (data) => {
  if (data && data.categoryId && data.categoryId !== 0) {
    queryParams.fullCode = data.fullCode;
    handleSelectCategoryInAddOperation(data.fullCode);
    getList();
  } else if (data && data.categoryId === 0) {
    queryParams.fullCode = undefined;
    getList();
  }
};

const handleSelectCategoryInAddOperation = (categoryCode) => {
  if(categoryCode) {
    // 获取categoryCode对应的分类名称
    console.log(categoryCode, categoryMap.value)
    categoryFullName.value = categoryMap.value[categoryCode].fullName;
    console.log(categoryMap.value[categoryCode].fullName)
    // category_code用逗号分隔
    const categoryCodes = categoryCode.split(".");
    // 取第一个分类的code作为物料的分类
    materialType.value = categoryCodes[0];
    // 如果categoryCodes.length > 2,则取第二个分类的code作为物料的形态，第三个做为子分类，否则第二个作为子类型
    if(categoryCodes.length > 2) {
      form.value = categoryCodes[1];
      subType.value = categoryCodes[2];
    } else {
      subType.value = categoryCodes[1];
    }
    console.log(formRef.value.formData);
    
  }
};

const priceRef = ref();
const handlePrice = (row: any) => {
  priceRef.value.openPrice(row);
}

// 加载和处理计量单位数据
const getUnits = () => {
  // 将unit转换为map对象
  UnitApi.getUnitPage({pageSize:100}).then(response => {
    console.log(response);
    unitList.value = response.list;
    unitList.value.forEach(unit => {
      unitMap.value[unit.id.toString()] = unit;
    });
  });
};
// 获取科目信息
const subjectList = ref([]);
const subjectMap = ref({});
const getSubjects = () => {
  AccountSubjectApi.getAccountSubjectList().then(response => {
    subjectList.value = response;
    subjectList.value.forEach(subject => {
      subjectMap.value[subject.subjectId] = subject;
    });
  });
};

/** 初始化 **/
onMounted(() => {
  getList()
  getCategoryList()
  getUnits()
  getSubjects()
})
</script>

<style scoped lang="scss">
  .inlineTagWrap{
    display: flex; 
    flex-direction: column;
    align-items: center; 
    gap: 2px;
    white-space: nowrap;
  }

  .inlineTag{
    display: inline-block;
  }
</style>
